'use client';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { ArrowUpDown, Code, Calendar } from 'lucide-react';
import { DataTableRowActions } from './row-actions';
import { DataTableCellContent } from './details-popover';
import { Badge } from '@/components/ui/badge';
import { formatDate } from '@/lib/utils';

export const createColumns = (dispatch) => {
	return [
		{
			id: 'select',
			header: ({ table }) => (
				<Checkbox
					checked={table?.getIsAllPageRowsSelected?.() || false}
					onCheckedChange={(value) =>
						table?.toggleAllPageRowsSelected?.(!!value)
					}
					aria-label="Select all"
				/>
			),
			cell: ({ row }) => (
				<Checkbox
					checked={row?.getIsSelected?.() || false}
					onCheckedChange={(value) => row?.toggleSelected?.(!!value)}
					aria-label="Select row"
				/>
			),
			enableSorting: false,
			enableHiding: false,
		},
		{
			accessorKey: 'code',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Code
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Code className="h-4 w-4 text-muted-foreground" />
					<span className="font-medium">{row.original.code}</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'name',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Project Name
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => <div className="font-medium">{row.original.name}</div>,
			enableSorting: true,
		},
		{
			accessorKey: 'status',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Status
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const status = row.original.status?.toLowerCase();
				let badgeVariant = 'default';

				switch (status) {
					case 'active':
						badgeVariant = { className: 'bg-green-500 hover:bg-green-600' };
						break;
					case 'pending':
						badgeVariant = 'warning';
						break;
					case 'completed':
						badgeVariant = 'secondary';
						break;
					case 'cancelled':
						badgeVariant = 'destructive';
						break;
					default:
						badgeVariant = 'outline';
				}

				return (
					<Badge {...badgeVariant} className="capitalize">
						{status}
					</Badge>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'lead',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Project Lead
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const lead = row.original.lead;
				if (!lead) return <div>No lead assigned</div>;

				return (
					<DataTableCellContent type="lead" value={lead.name} details={lead} />
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'createdAt',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Created
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const date = row.original.createdAt;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-muted-foreground" />
						<span>{date ? formatDate(date) : 'N/A'}</span>
					</div>
				);
			},
			enableSorting: true,
		},
		{
			id: 'actions',
			cell: ({ row }) => <DataTableRowActions row={row} dispatch={dispatch} />,
		},
	];
};
