'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import { X, User, Trash2 } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateTask } from '@/lib/features/tasks/tasksSlice';
import Image from 'next/image';
import { cn } from '@/lib/utils';

/**
 * TaskHeader Component
 * Renders the header section of the task modal with cover image, assignee selection, and close button
 */
export const TaskHeader = ({
	coverImage,
	onRemoveCoverImage,
	taskForm,
	onClose,
}) => {
	const { projectDetails } = useAppSelector((store) => store.projects);
	const { taskDetails } = useAppSelector((store) => store.tasks);
	const dispatch = useAppDispatch();

	const onSelectValueChange = (value) => {
		dispatch(
			updateTask({
				assignedTo: value,
				taskId: taskDetails._id,
				projectId: taskDetails.projectId,
			})
		);
	};
	return (
		<div className="relative">
			<div
				className={cn(
					'relative',
					coverImage ? 'h-32' : 'h-16',
					'border-b border-gray-700'
				)}
			>
				{coverImage && (
					<div className="group">
						<div className="absolute inset-0 bg-gray-100 dark:bg-gray-800">
							<Image
								src={coverImage.url}
								fill
								alt="Task cover"
								className="object-contain"
							/>
						</div>
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={onRemoveCoverImage}
							className="absolute bottom-2 right-2 h-7 px-2 text-xs bg-black/50 text-white hover:bg-black/70 transition-all opacity-0 group-hover:opacity-100"
						>
							Remove Cover
						</Button>
					</div>
				)}
				<div className="absolute top-4 right-4">
					<Button
						type="button"
						variant="ghost"
						size="sm"
						onClick={onClose}
						className="h-8 w-8 p-0"
					>
						<X className="h-4 w-4" />
					</Button>
				</div>
				<div className="absolute top-4 left-4">
					<div className="flex items-center gap-3">
						<FormField
							control={taskForm.control}
							name="assignedTo"
							render={({ field }) => (
								<FormItem className="flex-1">
									<Select
										onValueChange={onSelectValueChange}
										value={field.value}
									>
										<FormControl>
											<SelectTrigger className="h-8">
												<SelectValue placeholder="Select assignee" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{projectDetails?.assignedEmployees.map((assignee) => (
												<SelectItem
													key={assignee.userId}
													value={assignee.userId}
												>
													<div className="flex items-center gap-2">
														<Avatar className="h-5 w-5">
															<AvatarImage src={assignee.profilePhoto} />
															<AvatarFallback className="text-xs">
																{assignee.name
																	.split(' ')
																	.map((n) => n[0])
																	.join('')}
															</AvatarFallback>
														</Avatar>
														<span className="text-sm">{assignee.name}</span>
													</div>
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				</div>
			</div>
		</div>
	);
};
