import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	projects: [],
	employees: [],
	projectDetails: null,
	assignedProjects: [],
	isLoading: false,
	error: null,
};

export const fetchProjects = createAsyncThunk(
	'projects/fetchProjects',
	async (_, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get('/projects');
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const fetchSingleProjectDetails = createAsyncThunk(
	'projects/fetchSingleProjectDetails',
	async (projectId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(`/projects/${projectId}`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const createProject = createAsyncThunk(
	'projects/createProject',
	async (projectData, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.post('/projects', projectData);
			if (data.success) {
				dispatch(fetchProjects());
			}
			return data;
		} catch (error) {
			console.log(`💻 ~ projectsSlice.js:46 ~ error:`, error);
			rejectWithValue(error.response.data);
		}
	}
);

export const updateProject = createAsyncThunk(
	'projects/updateProject',
	async (projectData, { rejectWithValue, dispatch }) => {
		try {
			// console.log()
			const { data } = await customFetch.patch(`/projects`, projectData);
			if (data.success) {
				dispatch(fetchProjects());
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const deleteProject = createAsyncThunk(
	'projects/deleteProject',
	async (projectId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.delete(`/projects/${projectId}`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const fetchEmployeesForProject = createAsyncThunk(
	'projects/fetchEmployeesForProject',
	async (_, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(`/projects/employees`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const switchProjectLead = createAsyncThunk(
	'projects/switchProjectLead',
	async (projectData, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.patch(`/projects/change-lead`, {
				...projectData,
			});
			if (data.success) {
				dispatch(fetchProjects());
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const getAssignedProjects = createAsyncThunk(
	'projects/getAssignedProjects',
	async (_, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get('/projects/assigned-projects');
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

const projectsSlice = createSlice({
	name: 'projects',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchProjects.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchProjects.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.projects = payload.data;
				// toast.success(payload.message);
			})
			.addCase(fetchProjects.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchSingleProjectDetails.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchSingleProjectDetails.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.projectDetails = payload.data;
			})
			.addCase(fetchSingleProjectDetails.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateProject.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateProject.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateProject.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(deleteProject.pending, (state) => {
				state.isLoading = true;
				showErrors(payload);
			})
			.addCase(deleteProject.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.projects = state.projects.filter(
					(project) => project.id !== meta.arg
				);
			})
			.addCase(deleteProject.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchEmployeesForProject.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchEmployeesForProject.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.employees = payload.data;
			})
			.addCase(fetchEmployeesForProject.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(switchProjectLead.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(switchProjectLead.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(switchProjectLead.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(createProject.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(createProject.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(createProject.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(getAssignedProjects.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getAssignedProjects.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.assignedProjects = payload.data;
			})
			.addCase(getAssignedProjects.rejected, (state, { payload }) => {
				state.isLoading = false;
				// showErrors(payload);
			});
	},
});

export default projectsSlice.reducer;
