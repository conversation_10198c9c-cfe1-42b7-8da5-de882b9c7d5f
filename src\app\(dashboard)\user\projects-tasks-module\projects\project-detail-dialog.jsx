'use client';

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
	Calendar,
	Users,
	CheckCircle2,
	Clock,
	AlertCircle,
	Folder,
	Star,
	Edit,
	Trash2,
	Settings,
	Plus,
} from 'lucide-react';
import { cn } from '@/lib/utils';

export function ProjectDetailDialog({ project, open, onOpenChange }) {
	if (!project) return null;

	const getStatusIcon = (status) => {
		switch (status) {
			case 'completed':
				return <CheckCircle2 className="h-5 w-5 text-green-600" />;
			case 'active':
				return <Clock className="h-5 w-5 text-blue-600" />;
			case 'planning':
				return <AlertCircle className="h-5 w-5 text-orange-600" />;
			default:
				return <Folder className="h-5 w-5 text-muted-foreground" />;
		}
	};

	const getPriorityColor = (priority) => {
		switch (priority) {
			case 'high':
				return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
			case 'medium':
				return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
			case 'low':
				return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
		}
	};

	const getStatusColor = (status) => {
		switch (status) {
			case 'completed':
				return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
			case 'active':
				return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
			case 'planning':
				return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<div className="flex items-start justify-between">
						<div className="flex items-start gap-3">
							<div className={cn('w-1 h-12 rounded-full', project.color)} />
							<div>
								<DialogTitle className="text-2xl font-bold mb-2">
									{project.title}
								</DialogTitle>
								<DialogDescription className="text-base">
									{project.description}
								</DialogDescription>
							</div>
						</div>
						<div className="flex items-center gap-2">
							<Button variant="ghost" size="sm" className="h-8 w-8 p-0">
								<Star
									className={cn(
										'h-4 w-4',
										project.isStarred
											? 'fill-yellow-400 text-yellow-400'
											: 'text-muted-foreground'
									)}
								/>
							</Button>
							<Button variant="outline" size="sm">
								<Edit className="h-4 w-4 mr-2" />
								Edit
							</Button>
						</div>
					</div>
				</DialogHeader>

				<div className="space-y-6">
					{/* Status and Priority Row */}
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-4">
							<div className="flex items-center gap-2">
								{getStatusIcon(project.status)}
								<Badge
									variant="secondary"
									className={getStatusColor(project.status)}
								>
									{project.status}
								</Badge>
							</div>
							<div className="flex items-center gap-2">
								<span className="text-sm text-muted-foreground">Priority:</span>
								<Badge
									variant="secondary"
									className={getPriorityColor(project.priority)}
								>
									{project.priority}
								</Badge>
							</div>
						</div>
						<div className="flex items-center gap-2 text-sm text-muted-foreground">
							<Calendar className="h-4 w-4" />
							<span>Due: {new Date(project.dueDate).toLocaleDateString()}</span>
						</div>
					</div>

					<Separator />

					{/* Progress Section */}
					<div className="space-y-3">
						<div className="flex justify-between items-center">
							<h3 className="text-lg font-semibold">Progress</h3>
							<span className="text-2xl font-bold text-primary">
								{project.progress}%
							</span>
						</div>
						<Progress value={project.progress} className="h-3" />
						<div className="flex justify-between text-sm text-muted-foreground">
							<span>{project.completedTasks} completed</span>
							<span>{project.tasksCount} total tasks</span>
						</div>
					</div>

					<Separator />

					{/* Team Members Section */}
					<div className="space-y-4">
						<div className="flex items-center justify-between">
							<h3 className="text-lg font-semibold flex items-center gap-2">
								<Users className="h-5 w-5" />
								Team Members ({project.members.length})
							</h3>
							<Button variant="outline" size="sm">
								<Plus className="h-4 w-4 mr-2" />
								Add Member
							</Button>
						</div>
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
							{project.members.map((member) => (
								<div
									key={member.id}
									className="flex items-center gap-3 p-3 rounded-lg border bg-card"
								>
									<Avatar className="h-10 w-10">
										<AvatarImage src={member.avatar} />
										<AvatarFallback>
											{member.name
												.split(' ')
												.map((n) => n[0])
												.join('')}
										</AvatarFallback>
									</Avatar>
									<div className="flex-1">
										<div className="font-medium">{member.name}</div>
										<div className="text-sm text-muted-foreground">
											Team Member
										</div>
									</div>
								</div>
							))}
						</div>
					</div>

					<Separator />

					{/* Quick Actions */}
					<div className="space-y-4">
						<h3 className="text-lg font-semibold">Quick Actions</h3>
						<div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
							<Button variant="outline" className="h-auto flex-col gap-2 py-4">
								<CheckCircle2 className="h-5 w-5" />
								<span className="text-sm">View Tasks</span>
							</Button>
							<Button variant="outline" className="h-auto flex-col gap-2 py-4">
								<Calendar className="h-5 w-5" />
								<span className="text-sm">Timeline</span>
							</Button>
							<Button variant="outline" className="h-auto flex-col gap-2 py-4">
								<Settings className="h-5 w-5" />
								<span className="text-sm">Settings</span>
							</Button>
							<Button
								variant="outline"
								className="h-auto flex-col gap-2 py-4 text-destructive hover:text-destructive"
							>
								<Trash2 className="h-5 w-5" />
								<span className="text-sm">Delete</span>
							</Button>
						</div>
					</div>

					{/* Project Stats */}
					<div className="grid grid-cols-2 sm:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
						<div className="text-center">
							<div className="text-2xl font-bold text-primary">
								{project.tasksCount}
							</div>
							<div className="text-sm text-muted-foreground">Total Tasks</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-green-600">
								{project.completedTasks}
							</div>
							<div className="text-sm text-muted-foreground">Completed</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-orange-600">
								{project.tasksCount - project.completedTasks}
							</div>
							<div className="text-sm text-muted-foreground">Remaining</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-blue-600">
								{project.members.length}
							</div>
							<div className="text-sm text-muted-foreground">Team Size</div>
						</div>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
