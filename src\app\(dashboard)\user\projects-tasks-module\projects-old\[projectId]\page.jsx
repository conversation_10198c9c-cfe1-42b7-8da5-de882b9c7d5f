// 'use client';
// import { useState, useEffect } from 'react';
// import { Separator } from '@/components/ui/separator';
// import { Button } from '@/components/ui/button';
// import { Plus } from 'lucide-react';
// import { fetchSingleProjectDetails } from '@/lib/features/projects/projectsSlice';
// import { fetchTaskOfProject } from '@/lib/features/tasks/tasksSlice';
// import { useAppDispatch, useAppSelector } from '@/lib/hooks';
// import { SimpleLoader } from '@/components/loading-component';
// import TaskCard from '@/components/task/task-card';
// import { CreateTaskDialog } from './create-task-dialog';

// export default function TasksPage({ params }) {
// 	// const [isDialogOpen, setIsDialogOpen] = useState(false);
// 	const projectId = params?.projectId;
// 	const dispatch = useAppDispatch();
// 	const { tasks } = useAppSelector((store) => store.tasks);
// 	const { projectDetails, isLoading } = useAppSelector(
// 		(store) => store.projects
// 	);
// 	const [isDialogOpen, setIsDialogOpen] = useState(false);

// 	useEffect(() => {
// 		dispatch(fetchSingleProjectDetails(projectId));
// 		dispatch(fetchTaskOfProject(projectId));
// 	}, [dispatch, projectId]);

// 	if (isLoading) {
// 		return (
// 			<div className="w-full h-full flex items-center justify-center">
// 				<SimpleLoader />
// 			</div>
// 		);
// 	}

// 	return (
// 		<div className="container mx-auto py-10 px-2">
// 			<section className="flex items-center justify-between">
// 				<h1 className="text-2xl font-bold text-gray-700 dark:text-gray-100">
// 					Tasks of {projectDetails?.name}
// 				</h1>
// 				<Button
// 					onClick={() => setIsDialogOpen(true)}
// 					className="flex items-center gap-2"
// 				>
// 					<Plus className="h-4 w-4" />
// 					Add Tasks
// 				</Button>
// 			</section>
// 			<Separator className="my-2" />
// 			{tasks.data?.length > 0 ? (
// 				<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-4">
// 					{tasks.data?.map((task) => (
// 						<TaskCard key={task.id} task={task} />
// 					))}
// 				</div>
// 			) : (
// 				<div className="flex items-center justify-center h-full">
// 					No tasks found
// 				</div>
// 			)}

// 			<CreateTaskDialog open={isDialogOpen} setOpen={setIsDialogOpen} />
// 		</div>
// 	);
// }

'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { PlusCircle, LayoutGrid, List } from 'lucide-react';
import TaskCard from '@/components/task/task-card';
import ProjectOverview from '@/components/task/project-overview';
import CreateTaskDialog from '@/components/task/create-task-dialog';
import TaskDetailsDialog from '@/components/task/task-details-dialog';
import {
	mockTasks,
	mockTaskDetails,
	mockProjects,
	mockUsers,
} from '@/lib/mock-data';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchSingleTaskDetails,
	fetchTaskOfProject,
} from '@/lib/features/tasks/tasksSlice';

export default function TasksPage({ params }) {
	const projectId = params?.projectId;
	const [selectedTask, setSelectedTask] = useState(null);
	const [isCreateTaskOpen, setIsCreateTaskOpen] = useState(false);
	const [isViewTaskOpen, setIsViewTaskOpen] = useState(false);
	const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
	const dispatch = useAppDispatch();
	const { tasks, isLoading, taskDetails } = useAppSelector(
		(store) => store.tasks
	);

	useEffect(() => {
		// Simulate API call
		dispatch(fetchTaskOfProject(projectId));
	}, [dispatch, projectId]);

	const handleTaskClick = async (task) => {
		// Simulate fetching full task details
		await dispatch(fetchSingleTaskDetails(task._id));
		setSelectedTask(taskDetails);
		setIsViewTaskOpen(true);
	};

	const handleTaskCreated = (newTask) => {
		setTasks((prevTasks) => [
			{
				...newTask,
				_id: `new-${Date.now()}`,
				comments: [],
				media: newTask.media || [],
			}, // Simulate ID generation
			...prevTasks,
		]);
		setIsCreateTaskOpen(false);
	};

	const handleTaskUpdated = (updatedTask) => {
		setTasks((prevTasks) =>
			prevTasks.map((task) =>
				task._id === updatedTask._id ? { ...task, ...updatedTask } : task
			)
		);
		setSelectedTask((prevSelectedTask) =>
			prevSelectedTask && prevSelectedTask._id.$oid === updatedTask._id
				? { ...prevSelectedTask, ...updatedTask }
				: prevSelectedTask
		);
		// Potentially close dialog or refresh details
	};

	const handleCommentAdded = (taskId, newComment) => {
		// Update task in main list
		setTasks((prevTasks) =>
			prevTasks.map((task) =>
				task._id === taskId
					? { ...task, comments: [...(task.comments || []), newComment] }
					: task
			)
		);
		// Update selected task if it's the one being commented on
		if (selectedTask && selectedTask._id.$oid === taskId) {
			setSelectedTask((prev) => ({
				...prev,
				comments: [...(prev.comments || []), newComment],
			}));
		}
	};

	const handleStatusChange = (taskId, newStatus) => {
		setTasks((prevTasks) =>
			prevTasks.map((task) =>
				task._id === taskId ? { ...task, status: newStatus } : task
			)
		);
		if (selectedTask && selectedTask._id.$oid === taskId) {
			setSelectedTask((prev) => ({ ...prev, status: newStatus }));
		}
	};

	const handleDeleteTask = (taskId) => {
		setTasks((prevTasks) => prevTasks.filter((task) => task._id !== taskId));
		setIsViewTaskOpen(false);
		setSelectedTask(null);
	};

	// Group tasks by status for a potential Kanban/grouped list view
	const tasksByStatus = tasks.reduce((acc, task) => {
		const status = task.status || 'pending';
		if (!acc[status]) {
			acc[status] = [];
		}
		acc[status].push(task);
		return acc;
	}, {});

	const statusOrder = [
		'pending',
		'in-progress',
		'overdue',
		'completed',
		'cancelled',
	];

	return (
		<div className="container mx-auto p-4 md:p-6 bg-background text-foreground min-h-screen">
			<header className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
				<h1 className="text-3xl font-bold">Task Management</h1>
				<div className="flex items-center gap-2">
					<Button
						onClick={() => setIsCreateTaskOpen(true)}
						className="bg-primary hover:bg-primary/90 text-primary-foreground"
					>
						<PlusCircle className="mr-2 h-4 w-4" /> Create Task
					</Button>
					<Button
						variant="outline"
						size="icon"
						onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
					>
						{viewMode === 'grid' ? (
							<List className="h-4 w-4" />
						) : (
							<LayoutGrid className="h-4 w-4" />
						)}
						<span className="sr-only">Toggle view mode</span>
					</Button>
				</div>
			</header>

			{/* Project Overview Section */}
			<div className="mb-8">
				<ProjectOverview tasks={tasks} projects={mockProjects} />
			</div>

			{Object.keys(tasksByStatus).length === 0 ? (
				<div className="text-center text-muted-foreground py-10">
					<p className="text-xl">No tasks yet.</p>
					<p>Click $quot;Create Task$quot; to get started!</p>
				</div>
			) : (
				<div className="space-y-8">
					{statusOrder.map(
						(statusKey) =>
							tasksByStatus[statusKey] &&
							tasksByStatus[statusKey].length > 0 && (
								<section key={statusKey}>
									<h2 className="text-2xl font-semibold mb-4 capitalize border-b pb-2">
										{statusKey.replace('-', ' ')}
									</h2>
									<div
										className={
											viewMode === 'grid'
												? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
												: 'space-y-4'
										}
									>
										{tasksByStatus[statusKey].map((task) => (
											<TaskCard
												key={task._id}
												task={task}
												onClick={() => handleTaskClick(task)}
												viewMode={viewMode}
											/>
										))}
									</div>
								</section>
							)
					)}
				</div>
			)}

			{isCreateTaskOpen && (
				<CreateTaskDialog
					isOpen={isCreateTaskOpen}
					onClose={() => setIsCreateTaskOpen(false)}
					onTaskCreated={handleTaskCreated}
					projects={mockProjects}
					users={mockUsers}
				/>
			)}

			{selectedTask && isViewTaskOpen && (
				<TaskDetailsDialog
					isOpen={isViewTaskOpen}
					onClose={() => {
						setIsViewTaskOpen(false);
						setSelectedTask(null);
					}}
					task={selectedTask}
					projects={mockProjects}
					users={mockUsers}
					onTaskUpdated={handleTaskUpdated}
					onCommentAdded={handleCommentAdded}
					onStatusChange={handleStatusChange}
					onDeleteTask={handleDeleteTask}
				/>
			)}
		</div>
	);
}
