'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from '@/components/ui/command';
import {
	Plus,
	Calendar as CalendarIcon,
	Users,
	X,
	Palette,
	Image as ImageIcon,
	Edit,
	CircleX,
	Check,
	ChevronsUpDown,
	Crown,
} from 'lucide-react';
import { cn, generateRandomSixCharCode } from '@/lib/utils';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

// Import data
import { projectColors } from '../data/project-colors';
import { backgroundImages } from '../data/background-images';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	createProject,
	fetchEmployeesForProject,
	updateProject,
} from '@/lib/features/projects/projectsSlice';

// Form validation schema
const createProjectsSchema = z.object({
	code: z
		.string()
		.nonempty('Project code is required')
		.max(6, 'Project code must be at most 6 characters long'),
	name: z
		.string()
		.nonempty('Project name is required')
		.max(50, 'Project name must be at most 50 characters long'),
	description: z.string().optional(),
	status: z.enum(['active', 'inactive']).default('active'),
	lead: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Project lead is required'),
	employees: z
		.array(z.string().regex(/^[a-f\d]{24}$/i, 'Invalid ID'))
		.optional(),
	bgType: z.enum(['color', 'image']).default('color'),
	bgImage: z
		.object({
			url: z.string().optional(),
			thumbnail: z.string().optional(),
		})
		.nullable()
		.optional(),
	bgColor: z.string().nullable().optional(),
});

const updateProjectSchema = z.object({
	name: z.string().optional(),
	description: z.string().optional(),
	status: z.enum(['active', 'inactive']).optional(),
	bgType: z.enum(['color', 'image']).optional(),
	bgImage: z
		.object({
			url: z.string().optional(),
			thumbnail: z.string().optional(),
		})
		.nullable()
		.optional(),
	bgColor: z.string().nullable().optional(),
	lead: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Project lead is required'),
	employees: z
		.array(z.string().regex(/^[a-f\d]{24}$/i, 'Invalid ID'))
		.optional(),
});

export function CreateEditProjectDialog({
	children,
	project = null, // If provided, dialog is in edit mode
	open,
	onOpenChange,
}) {
	const { employees } = useAppSelector((store) => store.projects);
	const dispatch = useAppDispatch();
	const [internalOpen, setInternalOpen] = useState(false);
	const [selectedMembers, setSelectedMembers] = useState([]);
	const [employeesForAssign, setEmployeesForAssign] = useState([]);
	const [backgroundType, setBackgroundType] = useState('color');
	const [isEmployeeDropdownOpen, setIsEmployeeDropdownOpen] = useState(false);

	const isEditMode = !!project;
	const dialogOpen = open !== undefined ? open : internalOpen;
	const setDialogOpen = onOpenChange || setInternalOpen;

	const form = useForm({
		resolver: zodResolver(
			isEditMode ? updateProjectSchema : createProjectsSchema
		),
		defaultValues: {
			description: '',
			bgType: 'color',
			bgColor: '',
			bgImage: undefined,
			code: '',
			name: '',
			status: 'active',
			lead: '',
			employees: [],
		},
	});

	useEffect(() => {
		const code = generateRandomSixCharCode();
		form.setValue('code', code, { shouldDirty: true });
	}, [form, open]);

	useEffect(() => {
		dispatch(fetchEmployeesForProject());
	}, [dispatch]);

	// Populate form when in edit mode
	useEffect(() => {
		if (isEditMode && project) {
			form.reset({
				code: project.code,
				name: project.name,
				description: project.description,
				status: project.status,
				lead: project.lead,
				bgType: project.bgType || 'color',
				bgColor: project.bgColor,
				bgImage: project.bgImage,
				employees: project.employees || [],
			});
			setBackgroundType(project.bgType || 'color');

			// In edit mode, populate selectedMembers with actual employee objects
			// including the project lead
			console.log(project);
			if (employees && employees.length > 0) {
				setSelectedMembers(employees);
			} else {
				setSelectedMembers([]);
			}
		}
	}, [isEditMode, project, form]);

	useEffect(() => {
		console.log(form.formState.errors);
	}, [form.formState.errors]);

	const onSubmit = async (data) => {
		// Convert selectedMembers to employee IDs array
		const employeeIds = selectedMembers.map((member) => member.reportingUserId);

		console.log(data.bgColor, data.bgImage);
		if (data.bgType === 'color' && !data.bgColor) {
			data.bgColor = null;
			data.bgImage = null;
		}
		if (data.bgType === 'image' && !data.bgImage) {
			data.bgType = 'color';
			data.bgImage = null;
			data.bgColor = null;
		}
		const projectData = {
			...data,
		};
		console.log(projectData);

		let result;
		if (isEditMode) {
			result = await dispatch(
				updateProject({ projectId: project._id, ...projectData })
			);
		} else {
			result = await dispatch(
				createProject({ ...projectData, employees: employeeIds })
			);
		}

		if (
			createProject.fulfilled.match(result) ||
			updateProject.fulfilled.match(result)
		) {
			setDialogOpen(false);
		}
		if (!isEditMode) {
			form.reset();
			setSelectedMembers([]);
			setBackgroundType('color');
		}
	};

	const removeMember = (memberId) => {
		const leadId = form.watch('lead');

		// Prevent removing the project lead
		if (memberId === leadId) {
			return;
		}

		setSelectedMembers(
			selectedMembers.filter((m) => m.reportingUserId !== memberId)
		);
	};

	// For debugging form data
	// useEffect(() => {
	// 	const subscription = form.watch((value) => {
	// 		console.log(value);
	// 	});
	// 	return () => subscription.unsubscribe();
	// }, [form.watch, dispatch, form]);

	useEffect(() => {
		console.log(form.formState.errors);
	}, [form.formState.errors]);

	const toggleEmployee = (employee) => {
		const isSelected = selectedMembers.find(
			(m) => m.reportingUserId === employee.reportingUserId
		);
		if (isSelected) {
			setSelectedMembers(
				selectedMembers.filter(
					(m) => m.reportingUserId !== employee.reportingUserId
				)
			);
		} else {
			setSelectedMembers([...selectedMembers, employee]);
		}
	};

	// Handle project lead selection and automatic inclusion in employees
	useEffect(() => {
		const leadId = form.watch('lead');

		if (leadId && !isEditMode) {
			// Find the lead employee object
			const leadEmployee = employees.find(
				(emp) => emp.reportingUserId === leadId
			);

			if (leadEmployee) {
				// Check if lead is already in selectedMembers
				const isLeadAlreadySelected = selectedMembers.find(
					(member) => member.reportingUserId === leadId
				);

				// If lead is not already selected, add them automatically
				if (!isLeadAlreadySelected) {
					setSelectedMembers((prev) => [leadEmployee, ...prev]);
				}
			}
		}

		// Filter out the lead from available employees (they're automatically included)
		const employeesList = employees.filter(
			(emp) => form.watch('lead') !== emp.reportingUserId
		);
		setEmployeesForAssign(employeesList);
	}, [form.watch('lead'), employees, isEditMode]);

	// Separate useEffect for edit mode to ensure lead is always included
	useEffect(() => {
		if (isEditMode && project && project.lead) {
			const leadEmployee = employees.find(
				(emp) => emp.reportingUserId === project.lead
			);

			if (leadEmployee && selectedMembers.length > 0) {
				const isLeadInMembers = selectedMembers.find(
					(member) => member.reportingUserId === project.lead
				);

				// If lead is not in selectedMembers, add them
				if (!isLeadInMembers) {
					setSelectedMembers((prev) => [leadEmployee, ...prev]);
				}
			}
		}
	}, [isEditMode, project, employees, selectedMembers]);

	return (
		<Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
			{children && <DialogTrigger asChild>{children}</DialogTrigger>}
			<DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						{isEditMode ? (
							<>
								<Edit className="h-5 w-5" />
								Edit Project
							</>
						) : (
							<>
								<Plus className="h-5 w-5" />
								Create New Project
							</>
						)}
					</DialogTitle>
					<DialogDescription>
						{isEditMode
							? 'Update your project details and settings.'
							: 'Set up a new project to organize your tasks and collaborate with your team.'}
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						{/* Project Title */}
						<FormField
							control={form.control}
							name="code"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Project Code</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter project code"
											{...field}
											maxLength={6}
										/>
									</FormControl>
									<FormDescription>
										This ID is randomly generated but can be edited once
										according to your preference.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Project Name</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter project name"
											{...field}
											maxLength={50}
										/>
									</FormControl>
									<FormDescription>Maximum 50 characters</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
						{/* Project Description */}
						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Description</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Describe your project..."
											className="min-h-[100px]"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						{/* Priority and Due Date Row */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{/* Priority */}

							<FormField
								control={form.control}
								name="status"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Status</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select project status" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="active">Active</SelectItem>
												<SelectItem value="inactive">Inactive</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="lead"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Project Lead</FormLabel>
										<Select
											onValueChange={(value) => {
												field.onChange(value);
												// In edit mode, handle lead change
												if (isEditMode) {
													const newLead = employees.find(
														(emp) => emp.reportingUserId === value
													);
													const oldLeadId = form.getValues('lead');

													if (newLead) {
														// Remove old lead from selectedMembers if they exist
														const updatedMembers = selectedMembers.filter(
															(member) => member.reportingUserId !== oldLeadId
														);

														// Add new lead to selectedMembers if not already there
														const isNewLeadSelected = updatedMembers.find(
															(member) => member.reportingUserId === value
														);

														if (!isNewLeadSelected) {
															setSelectedMembers([newLead, ...updatedMembers]);
														} else {
															setSelectedMembers(updatedMembers);
														}
													}
												}
											}}
											value={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select a project lead" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{employees.map((employee) => (
													<SelectItem
														key={employee.reportingUserId}
														value={employee.reportingUserId}
													>
														{employee.reportingUserName}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormDescription>
											{isEditMode
												? 'Change the project lead (current lead will be updated in team members)'
												: 'Assign a project lead to this project'}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						{/* Background Selection with Tabs */}
						<FormField
							control={form.control}
							name="bgType"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Project Background</FormLabel>
									<FormControl>
										<Tabs
											value={field.value}
											onValueChange={(value) => {
												field.onChange(value);
												setBackgroundType(value);
											}}
											className="w-full"
										>
											<TabsList className="grid w-full grid-cols-2">
												<TabsTrigger
													value="color"
													className="flex items-center gap-2"
												>
													<Palette className="h-4 w-4" />
													Solid Color
												</TabsTrigger>
												<TabsTrigger
													value="image"
													className="flex items-center gap-2"
												>
													<ImageIcon className="h-4 w-4" />
													Background Image
												</TabsTrigger>
											</TabsList>

											<TabsContent value="color" className="mt-4">
												<FormField
													control={form.control}
													name="bgColor"
													render={({ field: colorField }) => (
														<div className="space-y-3">
															<div className="grid grid-cols-3 gap-3 max-h-60 overflow-y-auto">
																<button
																	type="button"
																	onClick={() => colorField.onChange(null)}
																	className={cn(
																		'relative aspect-video rounded-lg overflow-hidden border-2 transition-all bg-background',
																		colorField.value === null
																			? 'border-primary'
																			: 'border-muted'
																	)}
																	title="None"
																>
																	<CircleX className="h-12 w-12  absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-gray-400" />
																	<div className="absolute inset-0 bg-black/20 flex items-end p-2">
																		<span className="text-white text-xs font-medium drop-shadow-sm">
																			None
																		</span>
																	</div>
																</button>
																{projectColors.map((color) => (
																	<button
																		key={color.value}
																		type="button"
																		onClick={() =>
																			colorField.onChange(color.value)
																		}
																		className={cn(
																			'relative aspect-video rounded-lg overflow-hidden border-2 transition-all',
																			color.class,
																			colorField.value === color.value
																				? 'border-primary'
																				: 'border-muted'
																		)}
																		title={color.name}
																	>
																		<div className="absolute inset-0 bg-black/20 flex items-end p-2">
																			<span className="text-white text-xs font-medium drop-shadow-sm">
																				{color.name}
																			</span>
																		</div>
																	</button>
																))}
															</div>
															<FormDescription>
																Choose a solid color for your project background
																or select none
															</FormDescription>
														</div>
													)}
												/>
											</TabsContent>

											<TabsContent value="image" className="mt-4">
												<FormField
													control={form.control}
													name="bgImage"
													render={({ field: imageField }) => (
														<div className="space-y-3">
															<div className="grid grid-cols-3 gap-3 max-h-60 overflow-y-auto">
																<button
																	type="button"
																	onClick={() => imageField.onChange(null)}
																	className={cn(
																		'relative aspect-video rounded-lg overflow-hidden border-2 transition-all bg-background',
																		imageField.value === null
																			? 'border-primary'
																			: 'border-muted'
																	)}
																>
																	<CircleX className="h-12 w-12  absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-gray-400" />
																	<div className="absolute inset-0 bg-black/20 flex items-end p-2">
																		<span className="text-white text-xs font-medium">
																			None
																		</span>
																	</div>
																</button>
																{backgroundImages.map((image) => (
																	<button
																		key={image.id}
																		type="button"
																		onClick={() =>
																			imageField.onChange({
																				url: image.url,
																				thumbnail: image.thumbnail,
																			})
																		}
																		className={cn(
																			'relative aspect-video rounded-lg overflow-hidden border-2 transition-all',
																			imageField.value?.url === image.url
																				? 'border-primary'
																				: 'border-muted'
																		)}
																	>
																		<img
																			src={image.thumbnail}
																			alt={image.name}
																			className="w-full h-full object-cover"
																			loading="lazy"
																		/>
																		<div className="absolute inset-0 bg-black/20 flex items-end p-2">
																			<span className="text-white text-xs font-medium">
																				{image.name}
																			</span>
																		</div>
																	</button>
																))}
															</div>
															<FormDescription>
																Choose a background image for your project or
																select none
															</FormDescription>
														</div>
													)}
												/>
											</TabsContent>
										</Tabs>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						{/* Select Employees */}
						<div className="space-y-3">
							<FormLabel className="flex items-center gap-2">
								<Users className="h-4 w-4" />
								{isEditMode ? 'Edit Team Members' : 'Select Employees'}
							</FormLabel>

							{/* Selected Members - Stacked Circular Avatars */}
							{selectedMembers.length > 0 && (
								<div className="flex items-center gap-3">
									{/* Stacked Avatars */}
									<div className="flex -space-x-2">
										{selectedMembers.map((member, index) => (
											<div
												key={member.reportingUserId}
												className="relative group"
												style={{ zIndex: selectedMembers.length - index }}
											>
												<Avatar className="h-8 w-8 border-2 border-background ring-2 ring-white dark:ring-gray-800 transition-transform hover:scale-110 hover:z-50">
													<AvatarImage src={member.avatar} />
													<AvatarFallback className="text-xs font-medium bg-gradient-to-br from-blue-500 to-purple-600 text-white">
														{member.reportingUserName
															.split(' ')
															.map((n) => n[0])
															.join('')}
													</AvatarFallback>
												</Avatar>
												{/* Remove button on hover or Lead indicator */}
												{member.reportingUserId === form.watch('lead') ? (
													// Show lead indicator instead of remove button
													<div
														className="absolute -top-1 -right-1 h-4 w-4 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs z-50"
														title={`${member.reportingUserName} (Project Lead)`}
													>
														<Crown className="h-2.5 w-2.5" />
													</div>
												) : (
													// Show remove button for non-lead members
													<button
														type="button"
														onClick={() => removeMember(member.reportingUserId)}
														className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity text-xs z-50"
														title={`Remove ${member.reportingUserName}`}
													>
														<X className="h-2.5 w-2.5" />
													</button>
												)}
												{/* Tooltip with member name */}
												<div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
													{member.reportingUserName}
												</div>
											</div>
										))}
									</div>
									{/* Member count */}
									<span className="text-sm text-muted-foreground">
										{selectedMembers.length} employee
										{selectedMembers.length !== 1 ? 's' : ''} selected
									</span>
								</div>
							)}

							{/* Multi-Select Dropdown */}
							<Popover
								open={isEmployeeDropdownOpen}
								onOpenChange={setIsEmployeeDropdownOpen}
							>
								<PopoverTrigger asChild>
									<Button
										variant="outline"
										role="combobox"
										aria-expanded={isEmployeeDropdownOpen}
										className="w-full justify-between"
									>
										{selectedMembers.length > 0
											? `${selectedMembers.length} employee${selectedMembers.length !== 1 ? 's' : ''} selected`
											: 'Select employees...'}
										<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
									</Button>
								</PopoverTrigger>
								<PopoverContent className="w-full p-0">
									<Command>
										<CommandInput placeholder="Search employees..." />
										<CommandEmpty>No employees found.</CommandEmpty>
										<CommandList>
											<CommandGroup>
												{employeesForAssign.map((employee) => {
													const isSelected = selectedMembers.find(
														(m) =>
															m.reportingUserId === employee.reportingUserId
													);
													return (
														<CommandItem
															key={employee.reportingUserId}
															value={employee.reportingUserName}
															onSelect={() => toggleEmployee(employee)}
															className="flex items-center gap-3"
														>
															<div className="flex items-center gap-3 flex-1">
																<Avatar className="h-6 w-6">
																	<AvatarImage src={employee.avatar} />
																	<AvatarFallback className="text-xs">
																		{employee.reportingUserName
																			.split(' ')
																			.map((n) => n[0])
																			.join('')}
																	</AvatarFallback>
																</Avatar>
																<div>
																	<div className="font-medium">
																		{employee.reportingUserName}
																	</div>
																	<div className="text-sm text-muted-foreground">
																		{employee.email}
																	</div>
																</div>
															</div>
															<Check
																className={cn(
																	'h-4 w-4',
																	isSelected ? 'opacity-100' : 'opacity-0'
																)}
															/>
														</CommandItem>
													);
												})}
											</CommandGroup>
										</CommandList>
									</Command>
								</PopoverContent>
							</Popover>
							<FormDescription>
								{isEditMode
									? 'Add or remove team members (project lead cannot be removed)'
									: 'Select employees to collaborate on this project'}
							</FormDescription>
						</div>
						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => {
									form.reset();
									setDialogOpen(false);
								}}
							>
								Cancel
							</Button>
							<Button type="submit" variant="secondary">
								{isEditMode ? (
									<>
										<Edit className="h-4 w-4 mr-2" />
										Update Project
									</>
								) : (
									<>
										<Plus className="h-4 w-4 mr-2" />
										Create Project
									</>
								)}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
