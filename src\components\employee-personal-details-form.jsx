'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm, useWatch } from 'react-hook-form';
import { useEffect, useRef, useState } from 'react';
import { personalDetailsSchema } from '@/lib/schemas/employeeRegistrationSchema';
import { Check, ChevronsUpDown, Loader2 } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import {
	calculateAge,
	cn,
	generateRandomSixCharCode,
	getICFinPrefixes,
	userRoles,
} from '@/lib/utils';
import {
	fetchCountries,
	fetchDialCodes,
} from '@/lib/features/location/locationSlice';
import { religions } from '@/data/religions';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from './ui/command';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
	EyeIcon,
	EyeOffIcon,
	RefreshCw,
	PlusCircle,
	Trash2,
} from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { registerEmployee } from '@/lib/features/employees/employeeSlice';
import { registerOnboardingLinkPersonalDetails } from '@/lib/features/employees/employeeOnboardingSlice';
import ProfilePhotoUpload from './ui/profile-photo-uploader';
import { LoadingSubmitButton } from './loading-component';

export function EmployeePersonalDetailsForm() {
	const [isAddressManuallyEdited, setIsAddressManuallyEdited] = useState(false);
	const [selectedFile, setSelectedFile] = useState(null);
	const [isSingaporeCitizen, setIsSingaporeCitizen] = useState(false);
	const {
		countries,
		isLoading: isLoadingCountries,
		dialCodes,
	} = useAppSelector((store) => store.location);
	const { companyData } = useAppSelector((store) => store.companyDetails);
	const { authenticatedUser } = useAppSelector((store) => store.auth);
	const { isLoading } = useAppSelector((store) => store.employee);
	const { onBoardingEmployeeDetails } = useAppSelector(
		(store) => store.employeeOnboarding
	);
	const [showPassword, setShowPassword] = useState(false);
	const dispatch = useAppDispatch();
	const profilePhotoRef = useRef(null);

	const handleImageChange = (file) => {
		setSelectedFile(file);
		// console.log('File selected:', file?.name, file);
	};

	const userEmail =
		companyData?.ownerUserFlags?.isClientRegistrationAsEmployeeComplete ===
			false &&
		(authenticatedUser?.role === userRoles.CLIENT_ADMIN ||
			authenticatedUser?.role === userRoles.GLORIFIED_CLIENT_ADMIN)
			? authenticatedUser.email
			: undefined;

	const form = useForm({
		resolver: zodResolver(personalDetailsSchema),
		defaultValues: {
			employeeOrgId: '',
			email: '',
			// password: '',
			name: '',
			countryDialCode: '',
			mobile: '',
			gender: '',
			dateOfJoining: '',
			dob: '',
			age: '',
			nationality: '',
			residentialStatus: '',
			icFinNumber: '',
			icFinPrefix: '',
			issueDate: '',
			expiryDate: '',
			expiryDateReminder: '',
			religion: '',
			race: '',
			country: '',
			postalCode: '',
			streetName: '',
			houseNo: '',
			levelNo: '',
			unitNo: '',
			address: '',
			maritalStatus: 'single',
			spouseName: '',
			spouseEmploymentStatus: '',
			children: [],
		},
	});

	if (onBoardingEmployeeDetails?.email) {
		form.setValue('email', onBoardingEmployeeDetails?.email, {
			shouldDirty: true,
		});
		form.setValue('name', onBoardingEmployeeDetails?.name, {
			shouldDirty: true,
		});
	}

	if (userEmail) {
		form.setValue('email', userEmail, { shouldDirty: true });
	}

	const {
		fields: childrenFields,
		append: appendChild,
		remove: removeChild,
	} = useFieldArray({
		name: 'children',
		control: form.control,
	});

	const watchMaritalStatus = form.watch('maritalStatus');

	const watchFields = useWatch({
		control: form.control,
		name: [
			'dob',
			'nationality',
			'residentialStatus',
			'houseNo',
			'levelNo',
			'unitNo',
			'streetName',
			'postalCode',
		],
	});

	const [
		watchDob,
		watchNationality,
		watchResidentialStatus,
		houseNo,
		levelNo,
		unitNo,
		streetName,
		postalCode,
	] = watchFields;

	const watchChildren = useWatch({
		name: 'children',
		control: form.control,
	});

	useEffect(() => {
		dispatch(fetchCountries());
		dispatch(fetchDialCodes());
	}, [dispatch]);

	useEffect(() => {
		if (!watchChildren) return;

		watchChildren.forEach((child, index) => {
			if (!child.dob) return; // Skip if no DOB
			const age = calculateAge(child.dob);
			if (form.getValues(`children.${index}.age`) !== age) {
				form.setValue(`children.${index}.age`, age, { shouldDirty: true });
			}
		});
	}, [watchChildren, form]);

	// Efficient Age Calculation (Only Updates When Necessary)
	useEffect(() => {
		if (!watchDob) return;
		const age = calculateAge(watchDob);
		if (form.getValues('age') !== age) {
			form.setValue('age', age, { shouldDirty: true });
		}
	}, [watchDob, form]);

	// Auto-Fill Residential Status (Only if it's a Singapore)
	useEffect(() => {
		if (
			countries.find((country) => country._id === watchNationality)?.name ===
				'Singapore' &&
			form.getValues('residentialStatus') !== 'Singapore Citizen'
		) {
			form.setValue('residentialStatus', 'Singapore Citizen', {
				shouldDirty: true,
			});
			setIsSingaporeCitizen(true);
		}
	}, [watchNationality, form]);

	// Auto-Generate Address (Only Updates if Not Manually Edited)
	useEffect(() => {
		if (isAddressManuallyEdited) return;

		const addressParts = [
			houseNo && `BLK ${houseNo}`,
			(levelNo || unitNo) && `# ${levelNo || ''}${unitNo ? `-${unitNo}` : ''}`,
			streetName,
			postalCode,
		].filter(Boolean);

		const fullAddress = addressParts.join(',\n');

		if (fullAddress !== form.getValues('address')) {
			form.setValue('address', fullAddress, { shouldDirty: true });
		}
	}, [
		houseNo,
		levelNo,
		unitNo,
		streetName,
		postalCode,
		isAddressManuallyEdited,
		form,
	]);

	// Form Submission Handling
	const onSubmit = async (data) => {
		const finalData = {
			...data,
			icFinNumber: data.icFinPrefix + data.icFinNumber,
			children: Array.isArray(data.children) ? data.children : [],
		};
		const formData = new FormData();

		// Append form fields
		Object.entries(finalData).forEach(([key, value]) => {
			// Convert arrays/objects to JSON
			if (Array.isArray(value) || typeof value === 'object') {
				formData.append(key, JSON.stringify(value));
			} else {
				formData.append(key, value);
			}
		});

		// Append profile photo if available
		// if (profilePhotoRef.current?.files[0] ) {
		// 	formData.append('profilePhoto', profilePhotoRef.current.files[0]);
		// }
		if (selectedFile) {
			formData.append('profilePhoto', selectedFile);
		}

		if (onBoardingEmployeeDetails?.email) {
			dispatch(registerOnboardingLinkPersonalDetails(formData));
		} else {
			dispatch(registerEmployee(formData));
		}
	};

	/* Use this to generate secure password but need this code to be used on final submission

	// // Generate Secure Password
	// const generatePassword = () => {
	// 	const charset =
	// 		'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';
	// 	const password = Array.from({ length: 12 }, () =>
	// 		charset.charAt(Math.floor(Math.random() * charset.length))
	// 	).join('');
	// 	form.setValue('password', password, { shouldDirty: true });
	// };
	
	*/

	useEffect(() => {
		const employeeOrgId = generateRandomSixCharCode();
		form.setValue('employeeOrgId', employeeOrgId, { shouldDirty: true });
	}, [form]);

	// useEffect(() => {
	// 	const subscription = form.watch(value => {console.log(value)})
	// 	return () => subscription.unsubscribe();
	// },[form])

	// useEffect(() => {
	// 	console.log(form.formState.errors);
	// }, [form.formState.errors]);

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
				{/* Basic Information */}
				<div className="space-y-4">
					{/* <h3 className="text-lg font-medium">Basic Information</h3> */}
					{/* <Separator /> */}
					<article className="flex flex-col-reverse lg:flex-row gap-4 items-center justify-between md:items-end">
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 flex-grow">
							<FormField
								control={form.control}
								name="employeeOrgId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Employee ID</FormLabel>
										<FormControl>
											<Input placeholder="Enter employee ID" {...field} />
										</FormControl>
										<FormDescription>
											This ID is randomly generated but can be edited once
											according to your preference.
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Email</FormLabel>
										<FormControl>
											<Input
												type="email"
												placeholder="Enter email"
												readOnly={
													companyData?.ownerUserFlags
														?.isClientRegistrationAsEmployeeComplete ===
														false || onBoardingEmployeeDetails?.email
												}
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="dateOfJoining"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Date of Joining</FormLabel>
										<FormControl>
											<Input type="date" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<section className="flex flex-col items-center w-full lg:w-auto">
							<ProfilePhotoUpload onImageChange={handleImageChange} size="lg" />
							<div className="text-sm text-muted-foreground text-center mt-2">
								{selectedFile ? (
									<p>Selected: {selectedFile.name}</p>
								) : (
									<p>Click the circle to upload your profile photo</p>
								)}
							</div>
						</section>
					</article>
				</div>

				{/* Personal Information */}
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Personal Information</h3>
					<Separator />
					{/* <div className="grid grid-cols-12 gap-4"> */}
					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								// <FormItem className="col-span-12 lg:col-span-3">
								<FormItem>
									<FormLabel>Full Name as per NRIC</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter full name"
											readOnly={onBoardingEmployeeDetails?.name}
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						{/* <div className="grid grid-cols-3 gap-2 col-span-12 lg:col-span-3"> */}
						<div className="grid grid-cols-3 gap-2">
							<FormField
								control={form.control}
								name="countryDialCode"
								render={({ field }) => (
									<FormItem className="col-span-1">
										<FormLabel>Dial Code</FormLabel>
										<Popover>
											<PopoverTrigger asChild>
												<FormControl>
													<Button
														variant="outline"
														role="combobox"
														className={cn(
															'w-full justify-between',
															!field.value && 'text-muted-foreground'
														)}
													>
														{field.value
															? dialCodes.find((code) => code === field.value)
															: 'Code'}
														{isLoadingCountries && (
															<Loader2 className="h-4 w-4 animate-spin" />
														)}
														{!isLoadingCountries && (
															<ChevronsUpDown className="opacity-50" />
														)}
													</Button>
												</FormControl>
											</PopoverTrigger>
											<PopoverContent className="w-full p-0">
												<Command>
													<CommandInput
														placeholder="Search Dial Code..."
														className="h-9 w-full"
													/>
													<CommandList className="w-full">
														<CommandEmpty>No Dial Code found.</CommandEmpty>
														<CommandGroup>
															{dialCodes.map((code) => (
																<CommandItem
																	value={code}
																	key={code}
																	onSelect={() => {
																		form.setValue('countryDialCode', code);
																	}}
																>
																	{code}
																	<Check
																		className={cn(
																			'ml-auto',
																			code === field.value
																				? 'opacity-100'
																				: 'opacity-0'
																		)}
																	/>
																</CommandItem>
															))}
														</CommandGroup>
													</CommandList>
												</Command>
											</PopoverContent>
										</Popover>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="mobile"
								render={({ field }) => (
									<FormItem className="col-span-2">
										<FormLabel>Phone Number</FormLabel>
										<FormControl>
											{/* TODO: remove number arrows from input */}
											<Input
												type="tel"
												placeholder="Enter phone number"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<FormField
							control={form.control}
							name="gender"
							render={({ field }) => (
								// <FormItem className="col-span-12 lg:col-span-2">
								<FormItem>
									<FormLabel>Gender</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select gender" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="male">Male</SelectItem>
											<SelectItem value="female">Female</SelectItem>
											<SelectItem value="other">Other</SelectItem>
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="dob"
							render={({ field }) => (
								// <FormItem className="col-span-12 lg:col-span-2">
								<FormItem>
									<FormLabel>Date of Birth</FormLabel>
									<FormControl>
										<Input type="date" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="age"
							render={({ field }) => (
								// <FormItem className="col-span-12 lg:col-span-2">
								<FormItem>
									<FormLabel>Age</FormLabel>
									<FormControl>
										<Input
											type="number"
											max={120}
											readOnly
											{...field}
											placeholder="Generates Automatically"
										/>
									</FormControl>

									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="religion"
							render={({ field }) => (
								// <FormItem className="col-span-12 lg:col-span-2">
								<FormItem>
									<FormLabel>Religion</FormLabel>
									<Popover>
										<PopoverTrigger asChild>
											<FormControl>
												<Button
													variant="outline"
													role="combobox"
													className={cn(
														'w-full justify-between',
														!field.value && 'text-muted-foreground'
													)}
												>
													{field.value
														? religions.find(
																(religion) => religion.value === field.value
															)?.label
														: 'Select religion'}

													<ChevronsUpDown className="opacity-50" />
												</Button>
											</FormControl>
										</PopoverTrigger>
										<PopoverContent className="w-full p-0">
											<Command>
												<CommandInput
													placeholder="Search Religion..."
													className="h-9 w-full"
												/>
												<CommandList className="w-full">
													<CommandEmpty>No Religion found.</CommandEmpty>
													<CommandGroup>
														{religions.map((religion) => (
															<CommandItem
																value={religion.value}
																key={religion.label}
																onSelect={() => {
																	form.setValue('religion', religion.value);
																}}
															>
																{religion.label}
																<Check
																	className={cn(
																		'ml-auto',
																		religion.value === field.value
																			? 'opacity-100'
																			: 'opacity-0'
																	)}
																/>
															</CommandItem>
														))}
													</CommandGroup>
												</CommandList>
											</Command>
										</PopoverContent>
									</Popover>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="race"
							render={({ field }) => (
								// <FormItem className="col-span-12 lg:col-span-2">
								<FormItem>
									<FormLabel>Race</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select race" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{/* Placeholder for race options from backend */}
											<SelectItem value="chinese">Chinese</SelectItem>
											<SelectItem value="eurasian">Eurasian</SelectItem>
											<SelectItem value="indian">Indian</SelectItem>
											<SelectItem value="malay">Malay</SelectItem>
											<SelectItem value="prefer-not-to-contribute">
												Prefer not to contribute
											</SelectItem>
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="nationality"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Nationality</FormLabel>
									<Popover>
										<PopoverTrigger asChild>
											<FormControl>
												<Button
													variant="outline"
													role="combobox"
													className={cn(
														'w-full justify-between',
														!field.value && 'text-muted-foreground'
													)}
												>
													{field.value
														? countries.find(
																(country) => country._id === field.value
															)?.name
														: 'Select country'}
													{isLoadingCountries && (
														<Loader2 className="h-4 w-4 animate-spin" />
													)}
													{!isLoadingCountries && (
														<ChevronsUpDown className="opacity-50" />
													)}
												</Button>
											</FormControl>
										</PopoverTrigger>
										<PopoverContent className="w-full p-0">
											<Command>
												<CommandInput
													placeholder="Search Country..."
													className="h-9"
												/>
												<CommandList>
													<CommandEmpty>No Country found.</CommandEmpty>
													<CommandGroup>
														{countries.map((country) => (
															<CommandItem
																value={country.name}
																key={country._id}
																onSelect={() => {
																	form.setValue('nationality', country._id);
																	if (country.name !== 'Singapore') {
																		setIsSingaporeCitizen(false);
																	}
																}}
															>
																{country.name}
																<Check
																	className={cn(
																		'ml-auto',
																		country._id === field.value
																			? 'opacity-100'
																			: 'opacity-0'
																	)}
																/>
															</CommandItem>
														))}
													</CommandGroup>
												</CommandList>
											</Command>
										</PopoverContent>
									</Popover>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="residentialStatus"
							render={({ field }) => (
								// <FormItem className="col-span-12 lg:col-span-2">
								<FormItem>
									<FormLabel>Residential Status</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
										disabled={isSingaporeCitizen}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select residential status" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{!isSingaporeCitizen && (
												<>
													<SelectItem value="Singapore PR">
														Singapore PR
													</SelectItem>
													<SelectItem value="Employment Pass">
														Employment Pass
													</SelectItem>
													<SelectItem value="SPass">SPass</SelectItem>
													<SelectItem value="Work Permit">
														Work Permit
													</SelectItem>
													<SelectItem value="LOC">LOC</SelectItem>
												</>
											)}
											{isSingaporeCitizen && (
												<SelectItem value="Singapore Citizen">
													Singapore Citizen
												</SelectItem>
											)}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>
						{/* <div className="flex space-x-2 col-span-12 lg:col-span-2"> */}
						<div className="flex space-x-2 ">
							<FormField
								control={form.control}
								name="icFinPrefix"
								render={({ field }) => (
									<FormItem className="w-1/3">
										<FormLabel>Prefix</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Prefix" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{getICFinPrefixes(watchResidentialStatus).map(
													(prefix) => (
														<SelectItem key={prefix} value={prefix}>
															{prefix}
														</SelectItem>
													)
												)}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="icFinNumber"
								render={({ field }) => (
									<FormItem className="w-2/3">
										<FormLabel>
											{watchResidentialStatus === 'Singapore PR' ||
											isSingaporeCitizen
												? 'NRIC'
												: 'FIN'}
										</FormLabel>
										<FormControl>
											<Input
												placeholder={
													watchResidentialStatus === 'Singapore PR' ||
													isSingaporeCitizen
														? 'Enter NRIC'
														: 'Enter FIN'
												}
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						{!isSingaporeCitizen && (
							<FormField
								control={form.control}
								name="issueDate"
								render={({ field }) => (
									// <FormItem className="col-span-12 lg:col-span-2">
									<FormItem>
										<FormLabel>Issue Date</FormLabel>
										<FormControl>
											<Input type="date" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						)}
						{!isSingaporeCitizen &&
							watchResidentialStatus !== 'Singapore PR' && (
								<>
									<FormField
										control={form.control}
										name="expiryDate"
										render={({ field }) => (
											// <FormItem className="col-span-12 lg:col-span-2">
											<FormItem>
												<FormLabel>Expiry Date</FormLabel>
												<FormControl>
													<Input type="date" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="expiryDateReminder"
										render={({ field }) => (
											// <FormItem className="col-span-12 lg:col-span-2">
											<FormItem>
												<FormLabel>Expiry Date Reminder</FormLabel>
												<FormControl>
													<Input type="number" max="180" {...field} />
												</FormControl>
												<FormDescription>
													Set a reminder for expiry date in days.
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>
								</>
							)}
					</div>
				</div>

				{/* Address Information */}
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Address Information</h3>
					<Separator />
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						<FormField
							control={form.control}
							name="country"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Country</FormLabel>
									<Popover>
										<PopoverTrigger asChild>
											<FormControl>
												<Button
													variant="outline"
													role="combobox"
													className={cn(
														'w-full justify-between',
														!field.value && 'text-muted-foreground'
													)}
												>
													{field.value
														? countries.find(
																(country) => country._id === field.value
															)?.name
														: 'Select country'}
													{isLoadingCountries && (
														<Loader2 className="h-4 w-4 animate-spin" />
													)}
													{!isLoadingCountries && (
														<ChevronsUpDown className="opacity-50" />
													)}
												</Button>
											</FormControl>
										</PopoverTrigger>
										<PopoverContent className="w-full p-0">
											<Command>
												<CommandInput
													placeholder="Search Country..."
													className="h-9"
												/>
												<CommandList>
													<CommandEmpty>No Country found.</CommandEmpty>
													<CommandGroup>
														{countries.map((country) => (
															<CommandItem
																value={country.name}
																key={country._id}
																onSelect={() => {
																	form.setValue('country', country._id);
																}}
															>
																{country.name}
																<Check
																	className={cn(
																		'ml-auto',
																		country._id === field.value
																			? 'opacity-100'
																			: 'opacity-0'
																	)}
																/>
															</CommandItem>
														))}
													</CommandGroup>
												</CommandList>
											</Command>
										</PopoverContent>
									</Popover>
									<FormDescription>Your country of residence.</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="postalCode"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Postal Code</FormLabel>
									<FormControl>
										<Input placeholder="Enter postal code" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="streetName"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Street Name</FormLabel>
									<FormControl>
										<Input placeholder="Enter street name" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="houseNo"
							render={({ field }) => (
								<FormItem>
									<FormLabel>House / Block Number</FormLabel>
									<FormControl>
										<Input placeholder="Enter house number" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="levelNo"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Level Number</FormLabel>
									<FormControl>
										<Input placeholder="Enter level number" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="unitNo"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Unit Number</FormLabel>
									<FormControl>
										<Input placeholder="Enter unit number" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="address"
							render={({ field }) => (
								<FormItem className="col-span-full">
									<FormLabel>Full Address</FormLabel>
									<FormControl>
										<Textarea
											{...field}
											className="resize-none"
											rows={3}
											onBlur={(e) => {
												field.onBlur();
												setIsAddressManuallyEdited(true);
											}}
										/>
									</FormControl>
									<p className="text-sm text-muted-foreground">
										This field auto-generates from the address fields above but
										can be manually edited if needed.
									</p>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				</div>

				{/* Family Details */}
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Family Details</h3>
					<Separator />
					<div className="grid grid-cols-12 gap-6">
						<FormField
							control={form.control}
							name="maritalStatus"
							render={({ field }) => (
								<FormItem className="col-span-12 lg:col-span-4">
									<FormLabel>Marital Status</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select marital status" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="single">Single</SelectItem>
											<SelectItem value="married">Married</SelectItem>
											<SelectItem value="other">Other</SelectItem>
											<SelectItem value="prefer-not-to-say">
												Prefer not to say
											</SelectItem>
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						{watchMaritalStatus === 'married' && (
							<div className="col-span-12 lg:col-span-8 grid grid-cols-1 md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="spouseName"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Spouse Name</FormLabel>
											<FormControl>
												<Input placeholder="Enter spouse name" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="spouseEmploymentStatus"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Spouse Employment Status</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value || ''}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select employment status" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="employed">Employed</SelectItem>
													<SelectItem value="unemployed">Unemployed</SelectItem>
													<SelectItem value="prefer-not-to-say">
														Prefer not to say
													</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						)}
					</div>
					<div className="space-y-4">
						<h4 className="text-md font-medium">Children</h4>
						{childrenFields.map((field, index) => (
							<Card key={field.id} className="relative">
								<CardContent className="pt-6">
									<div className="grid grid-cols-1 md:grid-cols-4 gap-2">
										<FormField
											control={form.control}
											name={`children.${index}.name`}
											render={({ field }) => (
												<FormItem>
													<FormLabel>Name</FormLabel>
													<FormControl>
														<Input
															placeholder="Enter child's name"
															{...field}
															value={field.value || ''}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
										<FormField
											control={form.control}
											name={`children.${index}.dob`}
											render={({ field }) => (
												<FormItem>
													<FormLabel>Date of Birth</FormLabel>
													<FormControl>
														<Input
															type="date"
															className="w-full justify-between"
															{...field}
															value={field.value || ''}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
										<FormField
											control={form.control}
											name={`children.${index}.age`}
											render={({ field }) => (
												<FormItem>
													<FormLabel>Age</FormLabel>
													<FormControl>
														<Input
															type="text"
															{...field}
															value={field.value || '0'}
															readOnly
														/>
													</FormControl>
													<FormDescription>
														Age will be generated automatically on basis of date
														of birth
													</FormDescription>
													<FormMessage />
												</FormItem>
											)}
										/>
										<FormField
											control={form.control}
											name={`children.${index}.nationality`}
											render={({ field }) => (
												<FormItem>
													<FormLabel>Nationality</FormLabel>
													<Popover>
														<PopoverTrigger asChild>
															<FormControl>
																<Button
																	variant="outline"
																	role="combobox"
																	className={cn(
																		'w-full justify-between',
																		!field.value && 'text-muted-foreground'
																	)}
																>
																	{field.value
																		? countries.find(
																				(country) =>
																					country.name === field.value
																			)?.name
																		: 'Select country'}
																	{isLoadingCountries && (
																		<Loader2 className="h-4 w-4 animate-spin" />
																	)}
																	{!isLoadingCountries && (
																		<ChevronsUpDown className="opacity-50" />
																	)}
																</Button>
															</FormControl>
														</PopoverTrigger>
														<PopoverContent className="w-full p-0">
															<Command>
																<CommandInput
																	placeholder="Search Country..."
																	className="h-9 w-full"
																/>
																<CommandList className="w-full">
																	<CommandEmpty>No Country found.</CommandEmpty>
																	<CommandGroup>
																		{countries.map((country) => (
																			<CommandItem
																				value={country.name}
																				key={country._id}
																				onSelect={() => {
																					form.setValue(
																						`children.${index}.nationality`,
																						country.name
																					);
																				}}
																			>
																				{country.name}
																				<Check
																					className={cn(
																						'ml-auto',
																						country.name === field.value
																							? 'opacity-100'
																							: 'opacity-0'
																					)}
																				/>
																			</CommandItem>
																		))}
																	</CommandGroup>
																</CommandList>
															</Command>
														</PopoverContent>
													</Popover>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
									<Button
										type="button"
										variant="ghost"
										size="icon"
										onClick={() => removeChild(index)}
										className="absolute top-2 right-2"
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</CardContent>
							</Card>
						))}
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() =>
								appendChild({
									name: '',
									dob: '',
									age: '0',
									nationality: '',
								})
							}
						>
							<PlusCircle className="mr-2 h-4 w-4" />
							Add Child
						</Button>
					</div>
				</div>

				<div className="flex justify-end space-x-4">
					<LoadingSubmitButton
						isLoading={isLoading}
						buttonText={'Save and Next'}
						buttonLoadingText={'Saving...'}
					/>
				</div>
			</form>
		</Form>
	);
}
