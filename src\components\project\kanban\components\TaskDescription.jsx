'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import { RichTextEditor } from '../RichTextEditor';
import { useAppSelector } from '@/lib/hooks';

/**
 * TaskDescription Component
 * Renders the task description section with rich text editor
 */
export const TaskDescription = ({
	descriptionForm,
	onUpdateDescription,
	isGlassMode,
}) => {
	const { isLoading } = useAppSelector((store) => store.tasks);
	return (
		<div className="mb-6">
			<div className="flex items-center justify-between mb-2">
				<h3 className="text-sm font-medium text-card-foreground flex items-center gap-2">
					<span>Description</span>
				</h3>
			</div>

			<Form {...descriptionForm}>
				<FormField
					control={descriptionForm.control}
					name="description"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<RichTextEditor
									content={field.value}
									onChange={field.onChange}
									placeholder="Add a more detailed description..."
								/>
							</FormControl>
							<FormMessage />
							<div className="flex items-center gap-2 justify-end mt-2">
								{isLoading ? (
									<Button
										type="button"
										size="sm"
										className="h-7 px-2 text-xs"
										disabled
									>
										Saving...
									</Button>
								) : (
									<Button
										type="button"
										size="sm"
										className="h-7 px-2 text-xs"
										onClick={(e) => {
											e.preventDefault();
											e.stopPropagation();
											descriptionForm.handleSubmit(onUpdateDescription)();
										}}
									>
										Save Description
									</Button>
								)}
							</div>
						</FormItem>
					)}
				/>
			</Form>
		</div>
	);
};
