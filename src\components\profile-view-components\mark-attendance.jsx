'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import {
	CoffeeIcon,
	Laptop,
	CalendarCheck,
	ArrowRightLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
	TooltipProvider,
} from '@/components/ui/tooltip';
import dayjs from 'dayjs';
import ConfirmDialog from '@/components/confirm-dialog';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	getAttendanceLogs,
	getStatus,
	clockIn,
	clockOut,
	endBreak,
	startBreak,
	switchProject,
	fetchAssignedShiftDetails,
} from '@/lib/features/attendance/attendanceSlice';
import { SimpleLoader } from '@/components/loading-component';
import { Scroll<PERSON>rea } from '@/components/ui/scroll-area';
import { getAssignedProjects } from '@/lib/features/projects/projectsSlice';
import Link from 'next/link';
dayjs.extend(utc);
dayjs.extend(timezone);

export function MarkAttendance() {
	const dispatch = useAppDispatch();
	const {
		attendanceLogs,
		currentLogStatus,
		isLoading,
		shiftDetails,
		currentProject,
	} = useAppSelector((state) => state.attendance);
	const { assignedProjects: employeeProjects } = useAppSelector(
		(store) => store.projects
	);

	const [clockedIn, setClockedIn] = useState(false);
	const [canClockIn, setCanClockIn] = useState(false);
	const [canClockOut, setCanClockOut] = useState(false);
	const [isOnBreak, setIsOnBreak] = useState(false);
	const [clockInTooltipMsg, setClockInTooltipMsg] = useState('');
	const [clockOutTooltipMsg, setClockOutTooltipMsg] = useState('');
	const [currentTime, setCurrentTime] = useState(new Date());
	const [projectId, setProjectId] = useState(null);
	const [selectedProject, setSelectedProject] = useState(null);

	const fetchAttendanceRelatedDetails = useCallback(async () => {
		dispatch(fetchAssignedShiftDetails());
		dispatch(getStatus());
		dispatch(getAssignedProjects());
	}, [dispatch]);

	const formatTime = (date) => dayjs(date).format('hh:mm A');

	const calculateWorkedHours = useCallback(() => {
		let clockIn = null;
		let clockOut = null;
		let totalBreak = 0;

		attendanceLogs?.forEach((entry, i) => {
			if (entry.type === 'clockIn') clockIn = dayjs(entry.time);
			if (entry.type === 'clockOut') clockOut = dayjs(entry.time);
			if (entry.type === 'start-break') {
				const next = attendanceLogs[i + 1];
				const start = dayjs(entry.time);
				const end = next?.type === 'end-break' ? dayjs(next.time) : dayjs();
				totalBreak += end.diff(start);
			}
		});

		if (!clockIn) return '00:00';
		if (!clockOut) clockOut = dayjs();

		if (clockOut.isBefore(clockIn)) return '00:00';
		if (totalBreak > clockOut.diff(clockIn)) return '00:00';


		const totalWorked = clockOut.diff(clockIn) - totalBreak;
		const hours = Math.floor(totalWorked / 3600000);
		const minutes = Math.floor((totalWorked % 3600000) / 60000);
		const seconds = Math.floor((totalWorked % 60000) / 1000);
		return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
		// return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
	}, [attendanceLogs]);

	const checkClockingConditions = useCallback(() => {
		const now = dayjs();
		const today = dayjs().startOf('day');

		const todaysLogs =
			attendanceLogs?.filter((log) => dayjs(log.time).isAfter(today)) || [];

		const lastLog = todaysLogs[todaysLogs.length - 1];
		const lastLogTime = lastLog ? new Date(lastLog.time) : null;

		const totalClockIns = todaysLogs.filter(
			(log) => log.type === 'clockIn'
		).length;
		const totalClockOuts = todaysLogs.filter(
			(log) => log.type === 'clockOut'
		).length;

		setIsOnBreak(lastLog?.type === 'start-break');

		if (!attendanceLogs || attendanceLogs.length === 0) {
			setCanClockIn(true);
			setClockInTooltipMsg('');
			return;
		}
		const [hour, minute] = shiftDetails?.startTime.split(':');

		switch (lastLog?.type) {
			case 'clockIn': {
				setClockedIn(true);
				setCanClockIn(false);
				setClockInTooltipMsg('Already clocked in');
				if (totalClockOuts >= shiftDetails?.clockOutLimit) {
					setCanClockOut(false);
					setClockOutTooltipMsg(
						`Reached Clock Out limit (${shiftDetails?.clockOutLimit})`
					);
				} else if (
					dayjs(lastLogTime)
						.add(shiftDetails?.clockOutDelay, 'minute')
						.toDate() > now
				) {
					const waitMins = Math.ceil(
						(dayjs(lastLogTime)
							.add(shiftDetails?.clockOutDelay, 'minute')
							.toDate() -
							now) /
							60000
					);
					setCanClockOut(false);
					setClockOutTooltipMsg(`Wait ${waitMins} more minute(s) to Clock Out`);
				} else {
					setCanClockOut(true);
					setClockOutTooltipMsg('');
				}
				break;
			}
			case 'clockOut': {
				setClockedIn(false);
				setCanClockOut(false);
				setClockOutTooltipMsg('Already clocked out');
				if (totalClockIns >= shiftDetails?.clockInLimit) {
					setCanClockIn(false);
					setClockInTooltipMsg(
						`Reached Clock In limit (${shiftDetails?.clockInLimit})`
					);
				} else if (
					dayjs(lastLogTime)
						.add(shiftDetails?.clockInDelay, 'minute')
						.toDate() > now
				) {
					const waitMins = Math.ceil(
						(dayjs(lastLogTime)
							.add(shiftDetails?.clockInDelay, 'minute')
							.toDate() -
							now) /
							60000
					);
					setCanClockIn(false);
					setClockInTooltipMsg(`Wait ${waitMins} more minute(s) to Clock In`);
				} else {
					setCanClockIn(true);
					setClockInTooltipMsg('');
				}
				break;
			}
			case 'start-break':
				setClockedIn(true);
				setCanClockIn(false);
				setCanClockOut(false);
				setClockOutTooltipMsg('You are on a break');
				break;
			case 'end-break':
				setClockedIn(true);
				setCanClockIn(false);
				setCanClockOut(true);
				setClockOutTooltipMsg('');
				break;
			default:
				break;
		}
	}, [
		attendanceLogs,
		shiftDetails?.clockInDelay,
		shiftDetails?.clockOutDelay,
		shiftDetails?.clockInLimit,
		shiftDetails?.clockOutLimit,
	]);

	useEffect(() => {
		fetchAttendanceRelatedDetails();
	}, [fetchAttendanceRelatedDetails]);

	useEffect(() => {
		checkClockingConditions();
	}, [checkClockingConditions]);

	useEffect(() => {
		const timer = setInterval(() => {
			setCurrentTime(new Date());
			checkClockingConditions();
		}, 30000);
		return () => clearInterval(timer);
	}, [checkClockingConditions]);

	useEffect(() => {
		const realTimeClock = setInterval(() => {
			setCurrentTime(new Date());
		}, 1000);
		return () => clearInterval(realTimeClock);
	}, []);

	const handleClockIn = async (selectedProjectId) => {
		if (selectedProjectId) {
			await dispatch(clockIn({ projectId: selectedProjectId }));
			setProjectId(selectedProjectId);
			setSelectedProject(selectedProjectId);
		} else {
			await dispatch(clockIn());
		}
		dispatch(getAttendanceLogs());
		dispatch(getStatus());
	};

	const handleClockOut = async () => {
		await dispatch(clockOut());
		setProjectId(null);
		setSelectedProject(null);
		dispatch(getAttendanceLogs());
		dispatch(getStatus());
	};

	const handleStartBreak = async () => {
		await dispatch(startBreak());
		dispatch(getAttendanceLogs());
	};

	const handleEndBreak = async () => {
		await dispatch(endBreak());
		dispatch(getAttendanceLogs());
	};

	const handleSwitchProject = async (newProjectId) => {
		dispatch(switchProject({ projectId: newProjectId }));
		// console.log('Switching to project:', newProjectId);
		// setProjectId(newProjectId);
		// setSelectedProject(newProjectId);
	};

	const handleProjectChange = (projectId) => {
		// Handle project selection change in dialog
		console.log('Project changed to:', projectId);
	};

	const getLogDetails = useCallback((type) => {
		const map = {
			clockIn: { title: 'Clock In', color: 'bg-green-500' },
			clockOut: { title: 'Clock Out', color: 'bg-red-500' },
			'start-break': { title: 'Start Break', color: 'bg-yellow-500' },
			'end-break': { title: 'End Break', color: 'bg-blue-500' },
		};
		return map[type] || { title: type, color: 'bg-gray-500' };
	}, []);

	const recentLogs = useMemo(
		() => attendanceLogs?.slice(-6).reverse() || [],
		[attendanceLogs]
	);

	const shouldShowProjectSelect = employeeProjects.length > 0;
	const currentProjectName = currentProject;

	return (
		<TooltipProvider>
			<Card className="w-full h-full">
				<CardHeader>
					<div className="flex items-center justify-between">
						<CardTitle className="text-lg font-semibold text-card-foreground flex items-center gap-2">
							<CalendarCheck className="h-5 w-5" />
							Mark Attendance
							{/* // TODO: enable name of the project selected */}
							{clockedIn && shouldShowProjectSelect && (
								<span className="text-sm font-normal text-muted-foreground">
									- {currentProjectName}
								</span>
							)}
						</CardTitle>
						<div className="flex items-center gap-2">
							{isLoading && <SimpleLoader />}
							{!clockedIn ? (
								<Tooltip>
									<TooltipTrigger>
										<ConfirmDialog
											renderTrigger={
												<Button
													variant="outline"
													className="bg-green-100 text-green-800 hover:bg-green-200 border-green-300"
													disabled={isLoading || !canClockIn}
												>
													Clock In
												</Button>
											}
											title="Clock In Confirmation"
											description={`You will not be able to Clock Out for the next ${shiftDetails?.clockOutDelay} minutes${shouldShowProjectSelect ? '. Please select a project to work on.' : '.'}`}
											confirmTextClassName="bg-green-100 text-green-800 hover:bg-green-200"
											confirmText="Clock In"
											cancelText="Cancel"
											onConfirm={handleClockIn}
											dialogType="clockIn"
											projects={employeeProjects}
											showProjectSelect={shouldShowProjectSelect}
											selectedProject={selectedProject}
											onProjectChange={handleProjectChange}
										/>
									</TooltipTrigger>
									{!canClockIn && clockInTooltipMsg && (
										<TooltipContent className="bg-red-100 text-red-800">
											<p>{clockInTooltipMsg}</p>
										</TooltipContent>
									)}
								</Tooltip>
							) : (
								<>
									{isOnBreak ? (
										<Button
											onClick={handleEndBreak}
											variant="outline"
											size="icon"
											className="hover:bg-blue-50 bg-transparent"
										>
											<Laptop className="h-4 w-4" />
										</Button>
									) : (
										<Button
											onClick={handleStartBreak}
											variant="outline"
											size="icon"
											className="hover:bg-yellow-50 bg-transparent"
										>
											<CoffeeIcon className="h-4 w-4" />
										</Button>
									)}

									<Tooltip>
										<TooltipTrigger>
											<Button
												className="bg-red-500 text-white hover:bg-red-600 shadow-md"
												disabled={isLoading || !canClockOut}
												onClick={handleClockOut}
											>
												Clock Out
											</Button>
										</TooltipTrigger>
										{!canClockOut && clockOutTooltipMsg && (
											<TooltipContent className="bg-red-100 text-red-800">
												<p>{clockOutTooltipMsg}</p>
											</TooltipContent>
										)}
									</Tooltip>

									{shouldShowProjectSelect && (
										<Tooltip>
											<TooltipTrigger>
												<ConfirmDialog
													renderTrigger={
														<Button
															variant="outline"
															size="icon"
															className="hover:bg-blue-50 bg-transparent"
															disabled={isLoading}
														>
															<ArrowRightLeft className="h-4 w-4" />
														</Button>
													}
													title="Switch Project"
													description="Select a different project to work on. This will update your current project assignment."
													confirmTextClassName="bg-blue-100 text-blue-800 hover:bg-blue-200"
													confirmText="Switch Project"
													cancelText="Cancel"
													onConfirm={handleSwitchProject}
													dialogType="switchProject"
													projects={employeeProjects}
													showProjectSelect={true}
													selectedProject={selectedProject}
													onProjectChange={handleProjectChange}
												/>
											</TooltipTrigger>
											<TooltipContent>
												<p>Switch Project</p>
											</TooltipContent>
										</Tooltip>
									)}

									{/* <Tooltip>
										<TooltipTrigger>
											<Button
												onClick={() => {
													dispatch(getAttendanceLogs());
													dispatch(getStatus());
												}}
												variant="outline"
												size="icon"
												className="hover:bg-gray-50"
											>
												<RefreshCwIcon className="h-4 w-4" />
											</Button>
										</TooltipTrigger>
										<TooltipContent>
											<p>Refresh attendance data</p>
										</TooltipContent>
									</Tooltip> */}
								</>
							)}
						</div>
					</div>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
						<div className="lg:col-span-1">
							<ScrollArea className="h-[217px] rounded-lg">
								{recentLogs.length > 0 ? (
									<div className="space-y-2">
										{recentLogs.map((log, index) => {
											const details = getLogDetails(log.type);
											const isLatest = index === 0;
											return (
												<div
													key={index}
													className={`flex items-center gap-3 p-2 rounded-md border ${
														isLatest
															? 'bg-blue-50 border-blue-200'
															: 'bg-gray-50 border-gray-200'
													} hover:bg-gray-100 transition-colors`}
												>
													<div
														className={`w-2 h-2 rounded-full ${details.color} flex-shrink-0`}
													/>
													<div className="flex-1 min-w-0">
														<div className="flex items-center justify-between">
															<span className="text-sm font-medium text-gray-900">
																{details.title}
															</span>
															{isLatest && (
																<span className="text-xs text-blue-600 font-medium">
																	Latest
																</span>
															)}
														</div>
														<div className="text-xs text-gray-500">
															{dayjs(log.time).format('MMM DD, HH:mm')}
														</div>
														{/* <span>{details?.projectStats?.find(p => p.isWorking === true)?.projectId?.name}</span> */}
													</div>
												</div>
											);
										})}
									</div>
								) : (
									<div className="text-center py-6 text-gray-400">
										<p className="text-sm">No activity yet</p>
									</div>
								)}
							</ScrollArea>
						</div>
						<div className="flex flex-col items-center justify-start gap-2 min-w-[200px]">
							<div className="flex flex-col items-center justify-start gap-2 mt-4">
								<h3 className="text:lg font-semibold text-foreground">
									Total Time Worked
								</h3>
								<div className="font-bold font-mono text-foreground">
									<span className="lg:text-4xl">{calculateWorkedHours()}</span>
								</div>
								<div className="text-muted-foreground">
									{formatTime(currentTime)}
								</div>
							</div>
						</div>
					</div>
				</CardContent>

				{/* <CardFooter>
					<Button className="p-0" variant="link" asChild>
						<Link href="/client-admin/attendance-module">Timesheet</Link>
					</Button>
				</CardFooter> */}

			</Card>
		</TooltipProvider>
	);
}
