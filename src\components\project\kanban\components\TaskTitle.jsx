'use client';

import React, { useState, useRef } from 'react';
import { Input } from '@/components/ui/input';
import {
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateTask } from '@/lib/features/tasks/tasksSlice';

/**
 * TaskTitle Component
 * Renders the editable task title field with auto-save functionality
 */
export const TaskTitle = ({ taskForm }) => {
	const dispatch = useAppDispatch();
	const { taskDetails: task } = useAppSelector((store) => store.tasks);
	const { projectDetails: currentProject } = useAppSelector(
		(store) => store.projects
	);
	const [originalValue, setOriginalValue] = useState('');
	const timeoutRef = useRef(null);

	const handleTaskNameUpdate = async (newName) => {
		// Only update if the name has actually changed and is not empty
		if (newName.trim() && newName !== originalValue && task?._id) {
			try {
				await dispatch(
					updateTask({
						taskId: task._id,
						projectId: task.projectId || currentProject?.details?._id,
						name: newName.trim(),
					})
				);
				console.log('Task name updated successfully:', newName);
			} catch (error) {
				console.error('Error updating task name:', error);
			}
		}
	};

	const handleBlur = (field) => {
		const currentValue = field.value;
		handleTaskNameUpdate(currentValue);
	};

	const handleKeyDown = (e, field) => {
		if (e.key === 'Enter') {
			e.target.blur(); // Trigger onBlur to save
		}
	};

	// Debounced update for real-time typing (optional)
	const handleChange = (field, value) => {
		field.onChange(value);

		// Clear existing timeout
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
		}

		// Set new timeout for debounced update (2 seconds after user stops typing)
		timeoutRef.current = setTimeout(() => {
			handleTaskNameUpdate(value);
		}, 2000);
	};

	return (
		<div className="mb-6">
			<FormField
				control={taskForm.control}
				name="name"
				render={({ field }) => (
					<FormItem>
						<FormControl>
							<Input
								{...field}
								placeholder="Task title..."
								className="text-3xl font-semibold border-none p-0 h-auto focus-visible:ring-0"
								onFocus={() => setOriginalValue(field.value)}
								onBlur={() => handleBlur(field)}
								onKeyDown={(e) => handleKeyDown(e, field)}
								onChange={(e) => handleChange(field, e.target.value)}
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</div>
	);
};
